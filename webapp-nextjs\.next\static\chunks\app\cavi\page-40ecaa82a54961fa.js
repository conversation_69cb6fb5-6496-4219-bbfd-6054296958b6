(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{3469:(e,a,i)=>{Promise.resolve().then(i.bind(i,60569))},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>r});var t=i(95155);i(12115);var s=i(76981),n=i(5196),l=i(59434);function r(e){let{className:a,...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>r,rr:()=>p,zM:()=>o});var t=i(95155);i(12115);var s=i(15452),n=i(54416),l=i(59434);function r(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...i}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...i})}function m(e){let{className:a,children:i,showCloseButton:r=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[i,r&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...i})}function u(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...i})}function h(e){let{className:a,...i}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...i})}function p(e){let{className:a,...i}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...i})}},60569:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eC});var t=i(95155),s=i(12115),n=i(35695),l=i(66695),r=i(55365),o=i(40283),c=i(17522),d=i(13587),m=i(25731),x=i(30285),u=i(26126),h=i(47262);i(63743);var p=i(85127),b=i(62523),v=i(59409),g=i(20547),j=i(59434);let f=g.bL,N=g.l9,_=s.forwardRef((e,a)=>{let{className:i,align:s="center",sideOffset:n=4,...l}=e;return(0,t.jsx)(g.ZL,{children:(0,t.jsx)(g.UC,{ref:a,align:s,sideOffset:n,className:(0,j.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...l})})});_.displayName=g.UC.displayName;var y=i(21492),C=i(39881),w=i(58832),z=i(54416),A=i(66932),S=i(52278),k=i(42355),I=i(13052),E=i(12767);function O(e){let{data:a=[],columns:i=[],loading:n=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:m=!0,defaultRowsPerPage:h=25}=e,[b,g]=(0,s.useState)({key:null,direction:null}),[O,F]=(0,s.useState)({}),[D,L]=(0,s.useState)({}),[B,R]=(0,s.useState)(0),[M,P]=(0,s.useState)(h),U=e=>[...new Set(a.map(a=>a[e]).filter(Boolean))].sort(),V=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(O).forEach(a=>{let[i,t]=a;!t.value||Array.isArray(t.value)&&0===t.value.length||"string"==typeof t.value&&""===t.value.trim()||(e=e.filter(e=>{let a=e[i];if("select"===t.type)return(Array.isArray(t.value)?t.value:[t.value]).includes(a);if("text"===t.type){let e=t.value.toLowerCase(),i=String(a||"").toLowerCase();return"equals"===t.operator?i===e:i.includes(e)}if("number"===t.type){let e=parseFloat(a),i=parseFloat(t.value);if(isNaN(e)||isNaN(i))return!1;switch(t.operator){case"equals":default:return e===i;case"gt":return e>i;case"lt":return e<i;case"gte":return e>=i;case"lte":return e<=i}}return!0}))}),b.key&&b.direction&&e.sort((e,a)=>{let i=e[b.key],t=a[b.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===b.direction?-1:1;if(null==t)return"asc"===b.direction?1:-1;let s=parseFloat(i),n=parseFloat(t),l=!isNaN(s)&&!isNaN(n),r=0;return r=l?s-n:String(i).localeCompare(String(t)),"asc"===b.direction?r:-r}),e},[a,O,b]),$=(0,s.useMemo)(()=>{if(!m)return V;let e=B*M,a=e+M;return V.slice(e,a)},[V,B,M,m]);(0,s.useEffect)(()=>{R(0)},[O]);let J=Math.ceil(V.length/M),G=B*M+1,q=Math.min((B+1)*M,V.length);(0,s.useEffect)(()=>{o&&o(V)},[V,o]);let Z=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||g(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},W=(e,a)=>{F(i=>({...i,[e]:{...i[e],...a}}))},H=e=>{F(a=>{let i={...a};return delete i[e],i})},Y=e=>b.key!==e?(0,t.jsx)(y.A,{className:"h-3 w-3"}):"asc"===b.direction?(0,t.jsx)(C.A,{className:"h-3 w-3"}):"desc"===b.direction?(0,t.jsx)(w.A,{className:"h-3 w-3"}):(0,t.jsx)(y.A,{className:"h-3 w-3"}),X=Object.keys(O).length>0;return n?(0,t.jsx)(l.Zp,{className:d,children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:d,children:[X&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(O).map(e=>{let[a,s]=e,n=i.find(e=>e.field===a);if(!n)return null;let l=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,t.jsxs)(u.E,{variant:"secondary",className:"gap-1",children:[n.headerName,": ",l,(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>H(a),children:(0,t.jsx)(z.A,{className:"h-3 w-3"})})]},a)}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{F({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-0",children:(0,t.jsxs)(p.XI,{children:[(0,t.jsx)(p.A0,{children:(0,t.jsx)(p.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,t.jsx)(p.nd,{className:(0,j.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:e.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>Z(e.field),children:Y(e.field)}),!e.disableFilter&&(0,t.jsxs)(f,{open:D[e.field],onOpenChange:a=>L(i=>({...i,[e.field]:a})),children:[(0,t.jsx)(N,{asChild:!0,children:(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:(0,j.cn)("h-4 w-4 p-0 hover:bg-mariner-100",O[e.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(A.A,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(_,{className:"w-64",align:"start",children:(0,t.jsx)(T,{column:e,data:a,currentFilter:O[e.field],onFilterChange:a=>W(e.field,a),onClearFilter:()=>H(e.field),getUniqueValues:()=>U(e.field)})})]})]})]}),O[e.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,t.jsx)(p.BF,{children:$.length>0?$.map((e,a)=>c?c(e,B*M+a):(0,t.jsx)(p.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,t.jsx)(p.nA,{className:(0,j.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,t.jsx)(p.Hj,{children:(0,t.jsx)(p.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),m&&V.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(v.l6,{value:M.toString(),onValueChange:e=>{P(Number(e)),R(0)},children:[(0,t.jsx)(v.bq,{className:"w-20",children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"10",children:"10"}),(0,t.jsx)(v.eb,{value:"25",children:"25"}),(0,t.jsx)(v.eb,{value:"50",children:"50"}),(0,t.jsx)(v.eb,{value:"100",children:"100"}),(0,t.jsx)(v.eb,{value:V.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:V.length>0?"".concat(G,"-").concat(q," di ").concat(V.length):"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>R(0),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>R(e=>Math.max(0,e-1)),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>R(e=>Math.min(J-1,e+1)),disabled:B>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>R(J-1),disabled:B>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})]})]})]})}function T(e){let{column:a,currentFilter:i,onFilterChange:n,onClearFilter:l,getUniqueValues:r}=e,[o,c]=(0,s.useState)((null==i?void 0:i.value)||""),[d,m]=(0,s.useState)((null==i?void 0:i.operator)||"contains"),u=r(),p="number"!==a.dataType&&u.length<=20,g="number"===a.dataType,j=()=>{p?n({type:"select",value:Array.isArray(o)?o:[o]}):g?n({type:"number",value:o,operator:d}):n({type:"text",value:o,operator:d})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),p?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.S,{id:"filter-".concat(e),checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[g&&(0,t.jsxs)(v.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(v.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(v.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(v.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(v.eb,{value:"lte",children:"Minore o uguale"})]})]}),!g&&(0,t.jsxs)(v.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(b.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&j()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{size:"sm",onClick:j,children:"Applica"}),(0,t.jsx)(x.$,{size:"sm",variant:"outline",onClick:l,children:"Pulisci"})]})]})}var F=i(47924),D=i(28826),L=i(18979),B=i(27882);function R(e){let{cavi:a=[],onFilteredDataChange:i,loading:n=!1,selectionEnabled:r=!1,onSelectionToggle:o,selectedCount:c=0,totalCount:d=0}=e,[m,u]=(0,s.useState)(""),[h,p]=(0,s.useState)("contains"),g=e=>e?e.toString().toLowerCase().trim():"",j=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},f=(0,s.useCallback)((e,a,i)=>{let t=g(a);if(!t)return!0;let s=g(e.id_cavo),{prefix:n,number:l,suffix:r}=j(e.id_cavo||""),o=g(e.tipologia),c=g(e.formazione||e.sezione),d=g(e.utility),m=g(e.sistema),x=g(e.da||e.ubicazione_partenza),u=g(e.a||e.ubicazione_arrivo),h=g(e.utenza_partenza),p=g(e.utenza_arrivo),b=[s,n,l,r,o,c,d,m,x,u,h,p,g(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":g(e.id_bobina)],v=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return v.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&v.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?b.some(e=>e===t):b.some(e=>e.includes(t)))},[]),N=(0,s.useCallback)(()=>{if(!m.trim()){null==i||i(a);return}let e=m.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===h?1===e.length?a.filter(a=>f(a,e[0],!0)):a.filter(a=>e.every(e=>f(a,e,!0))):a.filter(a=>e.some(e=>f(a,e,!1))),null==i||i(t)},[m,h,a,i,f]);(0,s.useEffect)(()=>{N()},[N]);let _=e=>{u(e)},y=()=>{u(""),p("contains")};return(0,t.jsx)(l.Zp,{className:"mb-1",children:(0,t.jsxs)(l.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(b.p,{placeholder:"Cerca per ID, sistema, utility, tipologia, ubicazione...",value:m,onChange:e=>_(e.target.value),disabled:n,className:"pl-10 pr-10 h-8","aria-label":"Campo di ricerca intelligente per cavi"}),m&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:y,children:(0,t.jsx)(z.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(v.l6,{value:h,onValueChange:e=>p(e),children:[(0,t.jsx)(v.bq,{className:"h-8",children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]})}),m&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:y,disabled:n,className:"transition-all duration-200 hover:scale-105","aria-label":"Pulisci ricerca",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-1"}),"Pulisci"]}),o&&d>0&&(0,t.jsxs)(x.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105","aria-label":r?"Disabilita modalit\xe0 selezione":"Abilita modalit\xe0 selezione",children:[r?(0,t.jsx)(D.A,{className:"h-4 w-4"}):(0,t.jsx)(L.A,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]}),r&&c>0&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{},className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50","aria-label":"Deseleziona tutti i ".concat(c," cavi selezionati"),children:[(0,t.jsx)(B.A,{className:"h-4 w-4"}),"Deseleziona Tutto (",c,")"]})]}),m&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function M(e){let{text:a,maxLength:i=20,className:n=""}=e,[l,r]=(0,s.useState)(!1),[o,c]=(0,s.useState)({x:0,y:0});if(!a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>i,m=d?"".concat(a.substring(0,i),"..."):a;return d?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:"cursor-help ".concat(n),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),r(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>r(!1),title:a,children:m}),l&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:n,children:a})}var P=i(47650);let U=e=>{let{content:a,children:i,position:n="auto",delay:l=500,className:r="",disabled:o=!1,maxWidth:c=250}=e,[d,m]=(0,s.useState)(!1),[x,u]=(0,s.useState)(null),h=(0,s.useRef)(null),p=(0,s.useRef)(null),b=(0,s.useRef)(null),v=()=>{if(!h.current)return null;let e=h.current.getBoundingClientRect(),a=window.innerWidth,i=window.innerHeight,t=n,s=0,l=0;if("auto"===n){let s=e.top,n=i-e.bottom;e.left;let l=a-e.right;t=s>40&&s>n?"top":n>40?"bottom":l>c?"right":"left"}switch(t){case"top":s=e.top-40-8,l=e.left+e.width/2-c/2;break;case"bottom":s=e.bottom+8,l=e.left+e.width/2-c/2;break;case"left":s=e.top+e.height/2-20,l=e.left-c-8;break;case"right":s=e.top+e.height/2-20,l=e.right+8}return l=Math.max(8,Math.min(l,a-c-8)),{top:s=Math.max(8,Math.min(s,i-40-8)),left:l,position:t}},g=()=>{o||(b.current&&clearTimeout(b.current),b.current=setTimeout(()=>{let e=v();e&&(u(e),m(!0))},l))},j=()=>{b.current&&(clearTimeout(b.current),b.current=null),m(!1),u(null)};(0,s.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]);let f=d&&x?(0,t.jsxs)("div",{ref:p,className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ".concat(r),style:{top:x.top,left:x.left,maxWidth:c,wordWrap:"break-word",whiteSpace:"normal"},role:"tooltip","aria-hidden":!d,children:[a,(0,t.jsx)("div",{className:(e=>{let a="absolute w-0 h-0 border-solid";switch(e){case"top":return"".concat(a," top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900");case"bottom":return"".concat(a," bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900");case"left":return"".concat(a," left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900");case"right":return"".concat(a," right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900");default:return a}})(x.position)})]}):null;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:h,onMouseEnter:g,onMouseLeave:j,onFocus:g,onBlur:j,className:"inline-block","aria-describedby":d?"tooltip":void 0,children:i}),"undefined"!=typeof document&&f&&(0,P.createPortal)(f,document.body)]})},V=e=>{let{action:a,cableId:i,disabled:s=!1,children:n}=e;return(0,t.jsx)(U,{content:(()=>{if(s)switch(a){case"connect":return"Collegamento disponibile solo per cavi installati";case"disconnect":return"Nessun collegamento da scollegare";case"certify":return"Certificazione disponibile solo per cavi installati e collegati";case"generate_pdf":return"Generazione PDF disponibile solo per cavi certificati";default:return"Azione non disponibile"}switch(a){case"connect":return"Collega il cavo ".concat(i||""," alle sue destinazioni");case"disconnect":return"Scollega il cavo ".concat(i||""," da tutte le connessioni");case"certify":return"Certifica il cavo ".concat(i||""," dopo i test di collaudo");case"generate_pdf":return"Genera certificato PDF per il cavo ".concat(i||"");case"install":return"Inserisci metri installati per il cavo ".concat(i||"");case"modify":return"Modifica i dati della bobina per il cavo ".concat(i||"");default:return"Azione disponibile"}})(),disabled:!1,delay:300,position:"auto",children:n})},$=e=>{let{type:a,count:i,percentage:s,children:n}=e;return(0,t.jsx)(U,{content:(()=>{let e="".concat(i," cavi"),t=void 0!==s?" (".concat(s.toFixed(1),"%)"):"";switch(a){case"total":return"Totale cavi nel progetto: ".concat(e);case"installed":return"Cavi fisicamente installati: ".concat(e).concat(t);case"in_progress":return"Cavi in corso di installazione: ".concat(e).concat(t);case"to_install":return"Cavi ancora da installare: ".concat(e).concat(t);case"connected":return"Cavi completamente collegati: ".concat(e).concat(t);case"certified":return"Cavi certificati e collaudati: ".concat(e).concat(t);default:return e}})(),delay:200,position:"bottom",children:n})};var J=i(381),G=i(40646),q=i(85690),Z=i(82178),W=i(38164),H=i(77855),Y=i(91788),X=i(69037);function K(e){let{cavi:a=[],loading:i=!1,selectionEnabled:n=!1,selectedCavi:l=[],onSelectionChange:r,onStatusAction:o,onContextMenuAction:c}=e,[d,m]=(0,s.useState)(a),[b,v]=(0,s.useState)(a),[g,j]=(0,s.useState)(n);(0,s.useEffect)(()=>{m(a),v(a)},[a]);let f=e=>{r&&r(e?b.map(e=>e.id_cavo):[])},N=(e,a)=>{r&&r(a?[...l,e]:l.filter(a=>a!==e))},_=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:l,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download="cavi_export_".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert("Errore durante l'esportazione: ".concat(a.error))}}catch(e){alert("Errore durante l'esportazione")}},y=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:l,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert("Errore: ".concat(i.error))}catch(e){alert("Errore durante il cambio stato")}},C=()=>{alert("Assegnazione comanda per ".concat(l.length," cavi"))},w=async()=>{if(confirm("Sei sicuro di voler eliminare ".concat(l.length," cavi?")))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:l,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert("Errore: ".concat(a.error))}catch(e){alert("Errore durante l'eliminazione")}},A=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(M,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(M,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(M,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(M,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(M,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>{let a=e.id_bobina;if(a,!a||"N/A"===a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,t.jsx)(u.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50",children:"Vuota"});let i=a.match(/_B(.+)$/);return i||(i=a.match(/_b(.+)$/))||(i=a.match(/c\d+_[bB](\d+)$/))||(i=a.match(/(\d+)$/))?(0,t.jsx)("span",{className:"font-medium",children:i[1]}):(0,t.jsx)("span",{className:"font-medium text-xs",children:a})}},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableFilter:!0,disableSort:!0,renderCell:e=>S(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableFilter:!0,disableSort:!0,renderCell:e=>k(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableFilter:!0,disableSort:!0,renderCell:e=>I(e)}];return g&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(h.S,{checked:l.length===b.length&&b.length>0,onCheckedChange:f}),renderCell:e=>(0,t.jsx)(h.S,{checked:l.includes(e.id_cavo),onCheckedChange:a=>N(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[g,l,b,f,N]),S=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.comanda_posa,s=e.comanda_partenza,n=e.comanda_arrivo,l=e.comanda_certificazione,r=i||s||n||l;if(r&&"In corso"===e.stato_installazione)return(0,t.jsx)(V,{action:"install",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),null==o||o(e,"view_command",r)},children:[(0,t.jsx)(J.A,{className:"w-3 h-3 mr-1"}),r]})});let c=e.stato_installazione||"Da installare";return"Installato"===c||a>0?(0,t.jsx)(V,{action:"modify",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{className:"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",onClick:a=>{a.stopPropagation(),null==o||o(e,"modify_reel")},children:[(0,t.jsx)(G.A,{className:"w-3 h-3 mr-1"}),"Installato"]})}):"In corso"===c?(0,t.jsx)(V,{action:"install",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{className:"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",onClick:a=>{a.stopPropagation(),null==o||o(e,"insert_meters")},children:[(0,t.jsx)(q.A,{className:"w-3 h-3 mr-1"}),"In corso"]})}):(0,t.jsx)(V,{action:"install",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{variant:"outline",className:"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),null==o||o(e,"insert_meters")},children:[(0,t.jsx)(Z.A,{className:"w-3 h-3 mr-1"}),"Da installare"]})})},k=e=>{let a,i,s,n,l,r=e.metri_posati>0||e.metratura_reale>0,c=e.collegamento||e.collegamenti||0;if(!r)return(0,t.jsx)(V,{action:"connect",disabled:!0,children:(0,t.jsxs)(u.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:[(0,t.jsx)(z.A,{className:"w-3 h-3 mr-1"}),"Non disponibile"]})});switch(c){case 0:a="Collega",i="connect_cable",s="bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300",n=(0,t.jsx)(W.A,{className:"w-3 h-3 mr-1"}),l="connect";break;case 1:a="Completa Arrivo",i="connect_arrival",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",n=(0,t.jsx)(W.A,{className:"w-3 h-3 mr-1"}),l="connect";break;case 2:a="Completa Partenza",i="connect_departure",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",n=(0,t.jsx)(W.A,{className:"w-3 h-3 mr-1"}),l="connect";break;case 3:a="Scollega",i="disconnect_cable",s="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",n=(0,t.jsx)(H.A,{className:"w-3 h-3 mr-1"}),l="disconnect";break;default:a="Gestisci",i="manage_connections",s="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300",n=(0,t.jsx)(J.A,{className:"w-3 h-3 mr-1"}),l="connect"}return(0,t.jsx)(V,{action:l,cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{className:s,onClick:a=>{a.stopPropagation(),null==o||o(e,i)},children:[n,a]})})},I=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?(0,t.jsx)(V,{action:"generate_pdf",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{className:"bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),null==o||o(e,"generate_pdf")},children:[(0,t.jsx)(Y.A,{className:"w-3 h-3 mr-1"}),"Genera PDF"]})}):(0,t.jsx)(V,{action:"certify",cableId:e.id_cavo,children:(0,t.jsxs)(u.E,{variant:"outline",className:"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200",onClick:a=>{a.stopPropagation(),null==o||o(e,"create_certificate")},children:[(0,t.jsx)(X.A,{className:"w-3 h-3 mr-1"}),"Certifica"]})}):(0,t.jsx)(V,{action:"certify",disabled:!0,children:(0,t.jsxs)(u.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:[(0,t.jsx)(z.A,{className:"w-3 h-3 mr-1"}),"Non disponibile"]})})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(R,{cavi:a,onFilteredDataChange:e=>{m(e)},loading:i,selectionEnabled:g,onSelectionToggle:()=>{j(!g)}}),(0,t.jsx)(O,{data:d,columns:A,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{v(e)},renderRow:(e,a)=>{let i=l.includes(e.id_cavo);return(0,t.jsx)(p.Hj,{className:"\n          ".concat(i?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(i?"ring-1 ring-blue-300":"","\n        "),onClick:()=>g&&N(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),null==c||c(e,"context_menu")},children:A.map(a=>(0,t.jsx)(p.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(i?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),g&&l.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(u.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[l.length," cavi selezionati"]}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>f(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>y(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(x.$,{variant:"destructive",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})})]})}var Q=i(72713),ee=i(3493),ea=i(14186),ei=i(1243),et=i(71539),es=i(37108);function en(e){let{cavi:a,filteredCavi:i,className:n,revisioneCorrente:r}=e,o=(0,s.useMemo)(()=>{let e=a.length,t=i.length,s=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,n=i.filter(e=>"In corso"===e.stato_installazione).length,l=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,r=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-l)*2+(l-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:e,filteredCount:t,installati:s,inCorso:n,daInstallare:t-s-n,collegati:l,parzialmenteCollegati:r,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,i]);return(0,t.jsx)(l.Zp,{className:n,children:(0,t.jsxs)(l.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(Q.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:r&&(0,t.jsxs)(u.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsx)($,{type:"total",count:o.totalCavi,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(ee.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:o.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",o.totalCavi," cavi"]})]})]})}),(0,t.jsx)($,{type:"installed",count:o.installati,percentage:o.installati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi installati: ".concat(o.installati," cavi"),children:[(0,t.jsx)(G.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:o.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]})}),(0,t.jsx)($,{type:"in_progress",count:o.inCorso,percentage:o.inCorso/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi in corso: ".concat(o.inCorso," cavi"),children:[(0,t.jsx)(ea.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:o.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]})}),(0,t.jsx)($,{type:"to_install",count:o.daInstallare,percentage:o.daInstallare/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi da installare: ".concat(o.daInstallare," cavi"),children:[(0,t.jsx)(ei.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:o.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]})}),(0,t.jsx)($,{type:"connected",count:o.collegati,percentage:o.collegati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi collegati: ".concat(o.collegati," cavi"),children:[(0,t.jsx)(et.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:o.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]})}),(0,t.jsx)($,{type:"certified",count:o.certificati,percentage:o.certificati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi certificati: ".concat(o.certificati," cavi"),children:[(0,t.jsx)(es.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:o.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]})}),(0,t.jsx)($,{type:"total",count:o.metriInstallati,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[o.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",o.metriTotali.toLocaleString(),"m"]})]})]})})]}),o.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:"font-bold ".concat(o.percentualeInstallazione>=80?"text-emerald-700":o.percentualeInstallazione>=50?"text-yellow-700":o.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"),children:[o.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(o.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":o.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":o.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"),style:{width:"".concat(Math.min(o.percentualeInstallazione,100),"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[o.installati,"I + ",o.collegati,"C + ",o.certificati,"Cert"]})]})]})]})})}var el=i(54165),er=i(85057),eo=i(6740),ec=i(51154),ed=i(85339);function em(e){let{open:a,onClose:i,cavo:n,cantiere:l,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),h=l||u,[p,v]=(0,s.useState)([]),[g,j]=(0,s.useState)(""),[f,N]=(0,s.useState)(""),[_,y]=(0,s.useState)(""),[C,w]=(0,s.useState)(!1),[z,A]=(0,s.useState)(!1),[S,k]=(0,s.useState)(""),[I,E]=(0,s.useState)("compatibili"),O=async()=>{if(n)try{A(!0);let e=null==h?void 0:h.id_cantiere;if(!e){let a=localStorage.getItem("selectedCantiereId");(e=a?parseInt(a):0)&&0!==e||(e=1)}if(!e||0===e){v([]),k("Nessun cantiere selezionato. Seleziona un cantiere e riprova.");return}let a=await m.Fw.getBobineCompatibili(e,{tipologia:n.tipologia,n_conduttori:n.n_conduttori,sezione:n.sezione}),i=[];if(Array.isArray(a))i=a;else if(a&&Array.isArray(a.data))i=a.data;else if(a&&Array.isArray(a.bobine))i=a.bobine;else throw Error("Formato risposta API non valido");let t=i.map(e=>({id_bobina:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,fornitore:e.fornitore})),s=[{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",sezione:n.sezione||"",metri_residui:0},...t];n.id_bobina&&!s.find(e=>e.id_bobina===n.id_bobina)&&s.push({id_bobina:n.id_bobina,tipologia:n.tipologia||"",sezione:n.sezione||"",metri_residui:0}),v(s)}catch(a){let e=[{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",sezione:n.sezione||"",metri_residui:0}];n.id_bobina&&e.push({id_bobina:n.id_bobina,tipologia:n.tipologia||"",sezione:n.sezione||"",metri_residui:0}),v(e),d("Errore nel caricamento delle bobine disponibili")}finally{A(!1)}};(0,s.useEffect)(()=>{a&&n&&(O(),j(""),N(""),y(""),k(""))},[a,n,h,l,u]);let T=n?p.filter(e=>{let a=e.tipologia===n.tipologia&&e.sezione===n.sezione,i=!_||e.id_bobina.toLowerCase().includes(_.toLowerCase());return a&&i&&e.metri_residui>0}):[],D=n?p.filter(e=>{let a=e.tipologia!==n.tipologia||e.sezione!==n.sezione,i=!_||e.id_bobina.toLowerCase().includes(_.toLowerCase());return a&&i&&e.metri_residui>0}):[],L=async()=>{if(!n)return;if(!g)return void k("Selezionare un'operazione");if("assegna_nuova"===g&&!f)return void k("Selezionare una bobina dalla lista");let e="";if("annulla_installazione"===g)e="";else if("assegna_nuova"===g){if((e=f)===n.id_bobina)return void k("La bobina selezionata \xe8 gi\xe0 associata al cavo")}else if("rimuovi_bobina"!==g)return void k("Opzione non valida");else if((e="BOBINA_VUOTA")===n.id_bobina)return void k("Il cavo ha gi\xe0 la bobina vuota assegnata");if("assegna_nuova"===g){let e=p.find(e=>e.id_bobina===f),a=n.metri_posati||0;if(e&&a>e.metri_residui)return void k("La bobina selezionata ha solo ".concat(e.metri_residui,"m disponibili, ma il cavo ha ").concat(a,"m posati"))}try{if(w(!0),k(""),!h)throw Error("Cantiere non selezionato");console.log("\uD83D\uDD27 ModificaBobinaDialog: Operazione bobina:",{cantiere:h.id_cantiere,cavo:n.id_cavo,operazione:g,nuovaBobina:e}),"annulla_installazione"===g?await m.At.cancelInstallation(h.id_cantiere,n.id_cavo):await m.At.updateBobina(h.id_cantiere,n.id_cavo,e,!0);let a="";switch(g){case"assegna_nuova":a="Nuova bobina ".concat(e," assegnata al cavo ").concat(n.id_cavo);break;case"rimuovi_bobina":a="Bobina rimossa dal cavo ".concat(n.id_cavo," (metri restituiti alla bobina precedente)");break;case"annulla_installazione":a="Installazione annullata per il cavo ".concat(n.id_cavo,' (metri restituiti, stato resettato a "da installare")');break;default:a="Operazione completata per il cavo ".concat(n.id_cavo)}c(a),i()}catch(e){var a,t;d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante la modifica della bobina")}finally{w(!1)}},B=()=>{C||(j(""),N(""),y(""),k(""),E("compatibili"),i())};return n?(0,t.jsx)(el.lG,{open:a,onOpenChange:B,children:(0,t.jsxs)(el.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(el.c7,{children:(0,t.jsxs)(el.L3,{children:["Modifica Bobina Cavo ",n.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(es.A,{className:"h-5 w-5"}),(0,t.jsx)("h3",{className:"font-medium",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:n.id_cavo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:n.tipologia})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:n.sezione})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri:"}),(0,t.jsxs)("span",{className:"ml-1 font-medium",children:[n.metri_teorici," m"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,t.jsx)("span",{className:"ml-1 font-medium",children:n.id_bobina})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,t.jsx)("span",{className:"ml-1 px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium",children:n.stato_installazione})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===g,onChange:e=>j(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Assegna nuova bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===g,onChange:e=>j(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Rimuovi bobina attuale"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===g,onChange:e=>j(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Annulla installazione"})]})]})]}),"assegna_nuova"===g&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(b.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:_,onChange:e=>y(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>E("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===I?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",T.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>E("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===I?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",D.length,")"]})]})})]}),(0,t.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:z?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,t.jsx)("div",{className:"p-2",children:"compatibili"===I?0===T.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"space-y-1",children:T.map(e=>(0,t.jsx)("div",{onClick:()=>N(e.id_bobina),className:"p-3 rounded cursor-pointer transition-colors ".concat(f===e.id_bobina?"bg-blue-100 border border-blue-300":"hover:bg-gray-50 border border-transparent"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.id_bobina}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:[e.metri_residui,"m"]})})]})},e.id_bobina))}):0===D.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"space-y-1",children:D.map(e=>(0,t.jsx)("div",{onClick:()=>N(e.id_bobina),className:"p-3 rounded cursor-pointer transition-colors ".concat(f===e.id_bobina?"bg-orange-100 border border-orange-300":"hover:bg-gray-50 border border-transparent"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.id_bobina}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"30m"})})]})},e.id_bobina))})})})]})]}),S&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:S})]}),(0,t.jsxs)(el.Es,{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:B,disabled:C,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:L,disabled:C||!g||"bobina_compatibile"===g&&!f,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[C&&(0,t.jsx)(ec.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}function ex(e){let{open:a,onClose:i,cavo:n,cantiere:l,onSuccess:c,onError:d}=e,{cantiere:h}=(0,o.A)(),p=l||h,[v,g]=(0,s.useState)({metri_posati:"",id_bobina:""});(0,s.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!v.metri_posati,hasBobina:!!v.id_bobina,metri_posati:v.metri_posati,id_bobina:v.id_bobina})},[v]);let[j,f]=(0,s.useState)({}),[N,_]=(0,s.useState)({}),[y,C]=(0,s.useState)(!1),[w,A]=(0,s.useState)([]),[S,k]=(0,s.useState)(!1),[I,E]=(0,s.useState)(""),[O,T]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&n&&(p&&L(),g({metri_posati:"0",id_bobina:""}),f({}),_({}),E(""))},[a,n,p]),(0,s.useEffect)(()=>{v.metri_posati&&n?D(parseFloat(v.metri_posati)):(f(e=>({...e,metri_posati:void 0})),_(e=>({...e,metri_posati:void 0})))},[v.metri_posati,n]);let D=e=>{if(!n)return;let a={...j},i={...N};delete a.metri_posati,delete i.metri_posati,e>1.1*(n.metri_teorici||0)?i.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(n.metri_teorici,"m)"):e>(n.metri_teorici||0)&&(i.metri_posati="Metratura superiore ai metri teorici"),f(a),_(i)},L=async()=>{if(console.log("\uD83C\uDFAF InserisciMetriDialog: Caricamento bobine:",{cavo:!!n,cantiere:!!p,cavoId:null==n?void 0:n.id_cavo,cantiereId:null==p?void 0:p.id_cantiere}),n&&p)try{k(!0);let e=await m.Fw.getBobine(p.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(n){console.log("\uD83D\uDD0D InserisciMetriDialog: Filtro per cavo:",{tipologia:n.tipologia,sezione:n.sezione});let e=i.filter(e=>e.tipologia===n.tipologia&&e.sezione===n.sezione),a=i.filter(e=>e.tipologia!==n.tipologia||e.sezione!==n.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let t=[...e,...a];A(t)}else i.sort((e,a)=>a.metri_residui-e.metri_residui),A(i)}catch(t){var e,a,i;console.error("❌ InserisciMetriDialog: Errore caricamento bobine:",{message:t.message,response:t.response,status:null==(e=t.response)?void 0:e.status,data:null==(a=t.response)?void 0:a.data}),(null==(i=t.response)?void 0:i.status)!==404&&d("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),A([])}finally{k(!1)}},B=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=w.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},R=n?w.filter(e=>{let a=e.tipologia===n.tipologia&&e.sezione===n.sezione,i=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&i&&e.metri_residui>0}):[],M=n?w.filter(e=>{let a=e.tipologia!==n.tipologia||e.sezione!==n.sezione,i=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&i&&e.metri_residui>0}):[],P=e=>{g(a=>({...a,id_bobina:e.id_bobina})),f(e=>{let a={...e};return delete a.id_bobina,a})},U=async()=>{if(console.log("\uD83D\uDCBE InserisciMetriDialog: Salvataggio metri:",{cavo:null==n?void 0:n.id_cavo,metri_posati:v.metri_posati,id_bobina:v.id_bobina}),!n)return;if(!v.metri_posati||0>parseFloat(v.metri_posati))return void d("Inserire metri posati validi (≥ 0)");if(!v.id_bobina)return void d("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(v.metri_posati);if("BOBINA_VUOTA"!==v.id_bobina){let e=w.find(e=>e.id_bobina===v.id_bobina);e&&e.metri_residui}try{if(C(!0),!p)throw Error("Cantiere non selezionato");console.log("\uD83D\uDE80 InserisciMetriDialog: Chiamata API updateMetriPosati:",{cantiere:p.id_cantiere,cavo:n.id_cavo,metri:e,bobina:v.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===v.id_bobina}),await m.At.updateMetriPosati(p.id_cantiere,n.id_cavo,e,v.id_bobina,!0),c("Metri posati aggiornati con successo per il cavo ".concat(n.id_cavo,": ").concat(e,"m")),i()}catch(e){var a,t;d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{C(!1)}},V=()=>{y||(g({metri_posati:"",id_bobina:""}),f({}),_({}),E(""),i())};return n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(el.lG,{open:a,onOpenChange:V,children:(0,t.jsxs)(el.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(el.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eo.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",n.id_cavo]}),(0,t.jsx)(el.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",n.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",n.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",n.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",n.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",n.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",n.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.p,{id:"metri",type:"number",value:v.metri_posati,onChange:e=>g(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),j.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:j.metri_posati}),N.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(b.p,{placeholder:"ID, tipologia, formazione...",value:I,onChange:e=>E(e.target.value),className:"pl-10",disabled:y}),I&&(0,t.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>E(""),children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(x.$,{type:"button",variant:"BOBINA_VUOTA"===v.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===v.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{g(e=>({...e,id_bobina:"BOBINA_VUOTA"})),f(e=>{let a={...e};return delete a.id_bobina,a}),console.log("\uD83D\uDD04 InserisciMetriDialog: Usando BOBINA_VUOTA:",{saving:!1,metri_posati:v.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(j).length})},disabled:y,children:["BOBINA_VUOTA"===v.id_bobina&&(0,t.jsx)(G.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),S?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(ec.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),"Bobine Compatibili (",R.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===R.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:R.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(v.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>P(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(G.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:B(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",M.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===M.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:M.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(v.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>P(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,t.jsx)(G.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:B(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===w.length&&!S&&(0,t.jsxs)(r.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(r.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),j.id_bobina&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:j.id_bobina})]})]})]}),(0,t.jsxs)(el.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===n.stato_installazione&&n.id_bobina&&(0,t.jsx)(x.$,{variant:"outline",onClick:()=>{T(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:V,disabled:y,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:U,disabled:y||!v.metri_posati||0>parseFloat(v.metri_posati)||!v.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,t.jsx)(ec.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(em,{open:O,onClose:()=>T(!1),cavo:n,onSuccess:e=>{c(e),T(!1),i()},onError:d})]}):null}function eu(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:c}=e,{cantiere:d}=(0,o.A)(),[u,h]=(0,s.useState)(""),[p,b]=(0,s.useState)([]),[g,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(!1),[_,y]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&n&&(h(""),y(""),C())},[a,n]);let C=async()=>{if(d)try{N(!0);let e=await m.AR.getResponsabili(d.id_cantiere);b(e.data)}catch(e){b([])}finally{N(!1)}},w=async()=>{if(n&&d)try{j(!0),y(""),await m.At.collegaCavo(d.id_cantiere,n.id_cavo,"partenza",u),l("Collegamento lato partenza completato per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento")}finally{j(!1)}},z=async()=>{if(n&&d)try{j(!0),y(""),await m.At.collegaCavo(d.id_cantiere,n.id_cavo,"arrivo",u),l("Collegamento lato arrivo completato per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento")}finally{j(!1)}},A=async e=>{if(n&&d)try{j(!0),y(""),await m.At.scollegaCavo(d.id_cantiere,n.id_cavo,e);let a=e?" lato ".concat(e):"";l("Scollegamento".concat(a," completato per il cavo ").concat(n.id_cavo)),i()}catch(e){var a,t;c((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante lo scollegamento")}finally{j(!1)}};if(!n)return null;let S=(()=>{if(!n)return{stato:"non_collegato",descrizione:"Non collegato"};switch(n.collegamento||n.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}})(),k=(n.metri_posati||n.metratura_reale||0)>0;return(0,t.jsx)(el.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(el.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(el.c7,{children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(et.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",n.id_cavo]}),(0,t.jsxs)(el.rr,{children:["Gestisci i collegamenti del cavo ",n.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Stato Attuale"}),(0,t.jsx)("div",{className:"mt-1 text-lg font-semibold",children:S.descrizione})]}),!k&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Il cavo deve essere installato prima di poter essere collegato."})]}),_&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:_})]}),k&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"responsabile",children:"Responsabile Collegamento"}),(0,t.jsxs)(v.l6,{value:u,onValueChange:h,disabled:f,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(v.gC,{children:p.map(e=>(0,t.jsxs)(v.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:["partenza"!==S.stato&&"completo"!==S.stato&&(0,t.jsxs)(x.$,{onClick:w,disabled:g||!u,className:"w-full",children:[g?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Collega Partenza"]}),"arrivo"!==S.stato&&"completo"!==S.stato&&(0,t.jsxs)(x.$,{onClick:z,disabled:g||!u,className:"w-full",children:[g?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Collega Arrivo"]})]}),"non_collegato"!==S.stato&&(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)(x.$,{onClick:()=>A(),disabled:g,variant:"destructive",className:"w-full",children:[g?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"Scollega Completamente"]})})]})]})]}),(0,t.jsx)(el.Es,{children:(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:g,children:"Chiudi"})})]})})}var eh=i(88539);function ep(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:c}=e,{cantiere:d}=(0,o.A)(),[u,h]=(0,s.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[p,g]=(0,s.useState)([]),[j,f]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&n&&(h({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),C(""),w())},[a,n]);let w=async()=>{if(d)try{_(!0);let e=await m.AR.getResponsabili(d.id_cantiere);g(e.data)}catch(e){g([])}finally{_(!1)}},z=async()=>{if(n&&d){if(!u.responsabile_certificazione)return void C("Seleziona un responsabile per la certificazione");try{f(!0),C("");let e={id_cavo:n.id_cavo,responsabile_certificazione:u.responsabile_certificazione,data_certificazione:u.data_certificazione,esito_certificazione:u.esito_certificazione,note_certificazione:u.note_certificazione||null};await m.km.createCertificazione(d.id_cantiere,e),l("Certificazione completata per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la certificazione")}finally{f(!1)}}},A=async()=>{if(n&&d)try{f(!0),C("");let e=await m.km.generatePDF(d.id_cantiere,n.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","certificato_".concat(n.id_cavo,".pdf")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("PDF certificato generato per il cavo ".concat(n.id_cavo))}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la generazione del PDF")}finally{f(!1)}};if(!n)return null;let S=(n.metri_posati||n.metratura_reale||0)>0,k=!!n&&3===(n.collegamento||n.collegamenti||0),I=!!n&&(!0===n.certificato||"SI"===n.certificato||"CERTIFICATO"===n.certificato);return(0,t.jsx)(el.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(el.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(el.c7,{children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(X.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",n.id_cavo]}),(0,t.jsxs)(el.rr,{children:["Certifica il cavo ",n.id_cavo," o genera il PDF del certificato"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Stato Cavo"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(S?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:S?"Installato":"Non installato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(k?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:k?"Collegato":"Non collegato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(I?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:I?"Certificato":"Non certificato"})]})]})]}),!S&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Il cavo deve essere installato prima di poter essere certificato."})]}),!k&&S&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Il cavo deve essere completamente collegato prima di poter essere certificato."})]}),y&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:y})]}),I?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(X.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."})]}),(0,t.jsxs)(x.$,{onClick:A,disabled:j,className:"w-full",children:[j?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):S&&k&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"responsabile",children:"Responsabile Certificazione *"}),(0,t.jsxs)(v.l6,{value:u.responsabile_certificazione,onValueChange:e=>h(a=>({...a,responsabile_certificazione:e})),disabled:N,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(v.gC,{children:p.map(e=>(0,t.jsxs)(v.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"data",children:"Data Certificazione"}),(0,t.jsx)(b.p,{id:"data",type:"date",value:u.data_certificazione,onChange:e=>h(a=>({...a,data_certificazione:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"esito",children:"Esito Certificazione"}),(0,t.jsxs)(v.l6,{value:u.esito_certificazione,onValueChange:e=>h(a=>({...a,esito_certificazione:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)(v.eb,{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)(v.eb,{value:"CONFORME_CON_RISERVA",children:"CONFORME CON RISERVA"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eh.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:u.note_certificazione,onChange:e=>h(a=>({...a,note_certificazione:e.target.value})),rows:3})]}),(0,t.jsxs)(x.$,{onClick:z,disabled:j||!u.responsabile_certificazione,className:"w-full",children:[j?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo"]})]})]}),(0,t.jsx)(el.Es,{children:(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:j,children:"Chiudi"})})]})})}var eb=i(25273),ev=i(17580);function eg(e){let{open:a,onClose:i,caviSelezionati:n,tipoComanda:l,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),[h,p]=(0,s.useState)({tipo_comanda:l||"POSA",responsabile:"",note:""}),[b,g]=(0,s.useState)([]),[j,f]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(p({tipo_comanda:l||"POSA",responsabile:"",note:""}),C(""),w())},[a,l]);let w=async()=>{if(u)try{_(!0);let e=await m.AR.getResponsabili(u.id_cantiere);g(e.data)}catch(e){g([])}finally{_(!1)}},z=async()=>{if(u){if(!h.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===n.length)return void C("Seleziona almeno un cavo per la comanda");try{f(!0),C("");let e={tipo_comanda:h.tipo_comanda,responsabile:h.responsabile,note:h.note||null},a=await m.CV.createComandaWithCavi(u.id_cantiere,e,n);c("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(n.length," cavi")),i()}catch(i){var e,a;d((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,t.jsx)(el.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(el.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(el.c7,{children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eb.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(el.rr,{children:["Crea una nuova comanda per ",n.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(er.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",n.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[n.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),n.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",n.length-10," altri..."]})]})})]}),y&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:h.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(v.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(v.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(v.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(v.l6,{value:h.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:N,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(v.gC,{children:b.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ev.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eh.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:h.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(h.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",h.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",n.length," selezionati"]}),h.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",h.note]})]})]})]}),(0,t.jsxs)(el.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:j,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:z,disabled:j||!h.responsabile||0===n.length,children:[j?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var ej=i(29869),ef=i(64261);function eN(e){let{open:a,onClose:i,tipo:n,onSuccess:l,onError:c}=e,{cantiere:d}=(0,o.A)(),[u,h]=(0,s.useState)(null),[p,v]=(0,s.useState)(""),[g,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(""),[_,y]=(0,s.useState)(0),C=(0,s.useRef)(null),w=async()=>{if(u&&d){if("cavi"===n&&!p.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(j(!0),N(""),y(0),e="cavi"===n?await m.mg.importCavi(d.id_cantiere,u,p.trim()):await m.mg.importBobine(d.id_cantiere,u),y(100),e.data.success){let a=e.data.details,t=e.data.message;"cavi"===n&&(null==a?void 0:a.cavi_importati)?t+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===n&&(null==a?void 0:a.bobine_importate)&&(t+=" (".concat(a.bobine_importate," bobine importate)")),l(t),i()}else c(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{j(!1),y(0)}}},z=()=>{g||(h(null),v(""),N(""),y(0),C.current&&(C.current.value=""),i())},A=()=>"cavi"===n?"Cavi":"Bobine";return(0,t.jsx)(el.lG,{open:a,onOpenChange:z,children:(0,t.jsxs)(el.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(el.c7,{children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ej.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,t.jsxs)(el.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===n?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),f&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(b.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");h(i),N("")}},disabled:g,className:"flex-1"}),u&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(ef.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===n&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(er.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(b.p,{id:"revisione",value:p,onChange:e=>v(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:g}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),g&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(_,"%")}})})]}),u&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",u.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===n&&p&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",p]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==d?void 0:d.nome_cantiere]})]})]})]}),(0,t.jsxs)(el.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:z,disabled:g,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:w,disabled:g||!u||"cavi"===n&&!p.trim(),children:[g?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ej.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var e_=i(54213);function ey(e){let{open:a,onClose:i,onSuccess:n,onError:l}=e,{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,b]=(0,s.useState)(!1),[v,g]=(0,s.useState)(""),j=(e,a)=>{u(i=>({...i,[e]:a}))},f=async()=>{if(c)try{b(!0);let e=await m.mg.exportCavi(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),n("Export cavi completato con successo")}catch(i){var e,a;l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{b(!1)}},N=async()=>{if(c)try{b(!0);let e=await m.mg.exportBobine(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),n("Export bobine completato con successo")}catch(i){var e,a;l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{b(!1)}},_=async()=>{if(c)try{b(!0),g("");let e=[];d.cavi&&e.push(f()),d.bobine&&e.push(N()),d.comande,d.certificazioni,d.responsabili,await Promise.all(e);let a=Object.values(d).filter(Boolean).length;n("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{b(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(e_.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(ef.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(ef.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(ef.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(ef.A,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(el.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(el.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(el.c7,{children:[(0,t.jsxs)(el.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(Y.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(el.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==c?void 0:c.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[v&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:v})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,t.jsx)(h.S,{id:e.key,checked:d[e.key],onCheckedChange:a=>j(e.key,a),disabled:!e.available||p}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(er.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(d).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(er.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==c?void 0:c.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(d).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(el.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:p,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:_,disabled:p||0===Object.values(d).filter(Boolean).length,children:[p?(0,t.jsx)(ec.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(d).filter(Boolean).length>0?"(".concat(Object.values(d).filter(Boolean).length,")"):""]})]})]})})}function eC(){let{user:e,isAuthenticated:a,isLoading:i}=(0,o.A)(),{cantiereId:x,cantiere:u,isValidCantiere:h,isLoading:p,error:b}=(0,c.jV)(),v=(0,n.useRouter)(),g=e=>{let{title:a,description:i,variant:t}=e},[j,f]=(0,s.useState)([]),[N,_]=(0,s.useState)([]),[y,C]=(0,s.useState)(!0),[w,z]=(0,s.useState)(""),[A,S]=(0,s.useState)([]),[k,I]=(0,s.useState)(!0),[E,O]=(0,s.useState)([]),[T,F]=(0,s.useState)("");(0,s.useEffect)(()=>{O(j)},[j]);let[D,L]=(0,s.useState)({open:!1,cavo:null}),[B,R]=(0,s.useState)({open:!1,cavo:null}),[M,P]=(0,s.useState)({open:!1,cavo:null}),[U,V]=(0,s.useState)({open:!1,cavo:null}),[$,J]=(0,s.useState)({open:!1}),[G,q]=(0,s.useState)({open:!1}),[Z,W]=(0,s.useState)(!1),[H,Y]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0});(0,s.useEffect)(()=>{i||a||v.push("/login")},[a,i,v]);let X=u||(x&&x>0?{id_cantiere:x,commessa:"Cantiere ".concat(x)}:null);(0,s.useEffect)(()=>{h&&x&&x>0&&!p?(ee(),Q()):p||h||(f([]),_([]),z(b||"Nessun cantiere selezionato"))},[x,h,p,b]);let Q=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(x,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();F(a.revisione_corrente||"00")}else F("00")}catch(e){F("00")}},ee=async()=>{try{C(!0),z("");try{let e=await m.At.getCavi(x),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);f(a),_(i),ea(a)}catch(e){throw e}}catch(i){var e,a;z("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{C(!1)}},ea=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,n=e.reduce((e,a)=>e+(a.metri_teorici||0),0),l=e.reduce((e,a)=>e+(a.metri_posati||0),0);Y({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:n,metriInstallati:l,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},ei=(e,a,i)=>{switch(a){case"insert_meters":L({open:!0,cavo:e});break;case"modify_reel":R({open:!0,cavo:e});break;case"view_command":g({title:"Visualizza Comanda",description:"Apertura comanda ".concat(i," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":P({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":V({open:!0,cavo:e})}},et=(e,a)=>{switch(a){case"view_details":g({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":g({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":g({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":g({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":A.includes(e.id_cavo)?(S(A.filter(a=>a!==e.id_cavo)),g({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(S([...A,e.id_cavo]),g({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),g({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),g({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":g({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":g({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":J({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":J({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":J({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":J({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":g({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":g({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:g({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},el=e=>{g({title:"Operazione completata",description:e}),ee()},er=e=>{g({title:"Errore",description:e,variant:"destructive"})};return y&&h?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,t.jsx)(ec.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Caricamento cavi..."})]}):(0,t.jsx)(d.u,{children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[w&&(0,t.jsxs)(r.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:w})]}),(0,t.jsx)(en,{cavi:j,filteredCavi:E,revisioneCorrente:T,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(K,{cavi:j,loading:y,selectionEnabled:k,selectedCavi:A,onSelectionChange:S,onStatusAction:ei,onContextMenuAction:et})}),N.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(es.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",N.length,")"]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(K,{cavi:N,loading:y,selectionEnabled:!1,onStatusAction:ei,onContextMenuAction:et})})]})}),(0,t.jsx)(ex,{open:D.open,onClose:()=>L({open:!1,cavo:null}),cavo:D.cavo,cantiere:X,onSuccess:el,onError:er}),(0,t.jsx)(em,{open:B.open,onClose:()=>R({open:!1,cavo:null}),cavo:B.cavo,cantiere:X,onSuccess:el,onError:er}),(0,t.jsx)(eu,{open:M.open,onClose:()=>P({open:!1,cavo:null}),cavo:M.cavo,onSuccess:el,onError:er}),(0,t.jsx)(ep,{open:U.open,onClose:()=>V({open:!1,cavo:null}),cavo:U.cavo,onSuccess:el,onError:er}),(0,t.jsx)(eg,{open:$.open,onClose:()=>J({open:!1}),caviSelezionati:A,tipoComanda:$.tipoComanda,onSuccess:el,onError:er}),(0,t.jsx)(eN,{open:G.open,onClose:()=>q({open:!1}),tipo:G.tipo||"cavi",onSuccess:el,onError:er}),(0,t.jsx)(ey,{open:Z,onClose:()=>W(!1),onSuccess:el,onError:er})]})})}},88539:(e,a,i)=>{"use strict";i.d(a,{T:()=>l});var t=i(95155),s=i(12115),n=i(59434);let l=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:a,...s})});l.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,4295,1587,1807,861,9051,283,1642,3587,8441,1684,7358],()=>a(3469)),_N_E=e.O()}]);