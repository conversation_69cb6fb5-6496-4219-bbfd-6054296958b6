{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "f6261540691385213ed0b84c03d9da18", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6d1899ba968746eb78f17d16707c11828765634b2f8fce4827dc88f93e91197d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "00481601959f0c845fc56afe48e91b3d78384ab07a8275818562f2fec30eea05"}}}, "sortedMiddleware": ["/"], "functions": {}}